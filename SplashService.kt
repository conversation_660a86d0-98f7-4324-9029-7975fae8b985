package com.neoxia.myProject.service

import android.util.Log
import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.perf.metrics.AddTrace
import com.neoxia.myProject.api.HttpApi
import com.neoxia.myProject.model.SettingResponse
import com.neoxia.myProject.ui.splash.SplashActivity
import com.neoxia.myProject.ui.splash.SplashView
import com.neoxia.myProject.util.Constants
import com.neoxia.myProject.util.FuncUtil
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.observers.DisposableObserver
import io.reactivex.schedulers.Schedulers

@AddTrace(name = "get Version trace -fun")
class SplashService(var splashView: SplashView) {
    var httpApi: HttpApi

    init {
        httpApi = FuncUtil.createBuider(splashView as SplashActivity)
    }

    fun version() {
        Log.d("Version"," onNext")
        val myTrace = FirebasePerformance.getInstance().newHttpMetric(Constants.API_URL+"/api/version","get app version")
        myTrace.start()
        httpApi.version().subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<SettingResponse?>() {
                    override fun onComplete() {
                        myTrace.stop()
                    }

                    override fun onNext(t: SettingResponse) {
                        Log.d("Version"," onNext")
                        splashView.setting(t)
                    }

                    override fun onError(e: Throwable) {
                        Log.d("version"," onError")
                        splashView.setting(null)
                        e.printStackTrace()
                    }
                });
    }

}