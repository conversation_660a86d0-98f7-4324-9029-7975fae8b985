package com.neoxia.myProject.service

import android.util.Log
import com.google.firebase.perf.FirebasePerformance
import com.neoxia.myProject.api.HttpApi
import com.neoxia.myProject.model.*
import com.neoxia.myProject.ui.signin_signup.SignUpActivity
import com.neoxia.myProject.ui.signin_signup.SignUpStep2Activity
import com.neoxia.myProject.ui.signin_signup.SignUpView
import com.neoxia.myProject.util.Constants
import com.neoxia.myProject.util.FuncUtil
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.observers.DisposableObserver
import io.reactivex.schedulers.Schedulers
import retrofit2.*

import org.json.JSONObject
import java.lang.Exception


class SignUpService(var signupView: SignUpView) {
    var httpApi: HttpApi

    init {
        if(signupView is SignUpActivity)
            httpApi = FuncUtil.createBuider(signupView as SignUpActivity)
        else
            httpApi = FuncUtil.createBuider(signupView as SignUpStep2Activity)
    }

    fun signUpStep1(signUpStep1Request: SignUpStep1Request) {
        signupView.showLoader()
        val myTrace = FirebasePerformance.getInstance().newHttpMetric(Constants.API_URL+"/api/signupStep1","Sign uo Step1")
        myTrace.start()
        httpApi.signUpStep1(signUpStep1Request).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<SignUpResponse?>() {
                    override fun onComplete() {
                        myTrace.stop()
                    }

                    override fun onNext(t: SignUpResponse) {
                        Log.d("signUpStep1"," onNext")
                        signupView.onSignUpSuccess()
                        signupView.hideLoader()
                    }

                    override fun onError(e: Throwable) {
                        Log.d("signUpStep1"," onError")
                        try{
                            if((e as HttpException).code() == 400){
                                var errorBody = (e as HttpException).response().errorBody()
                                var body = FuncUtil.cloneResponseBody(errorBody)
                                var bodyString = FuncUtil.getBodyAsString(body)
                                var errorJson = JSONObject(bodyString)
                                var errorCode = errorJson.getString("errors").toString()
                                signupView.onSignUpFailer(errorCode)
                            }else{
                                signupView.onSignUpFailer("TECHNICAL_ERROR")
                            }
                        }catch(e : Exception){
                            signupView.onSignUpFailer("TECHNICAL_ERROR")
                        }

                        signupView.hideLoader()
                        e.printStackTrace()
                    }
                });
    }

    fun signUpStep2(signUpStep2Request: SignUpStep2Request) {
        signupView.showLoader()
        val myTrace = FirebasePerformance.getInstance().newHttpMetric(Constants.API_URL+"/api/signupStep2","Sign uo Step2")
        myTrace.start()
        httpApi.signUpStep2(signUpStep2Request).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<SignUpResponse?>() {
                    override fun onComplete() {
                        myTrace.stop()
                    }

                    override fun onNext(t: SignUpResponse) {
                        Log.d("signUpStep2"," onNext")
                        signupView.onSignUpSuccess()
                        signupView.hideLoader()
                    }

                    override fun onError(e: Throwable) {
                        Log.d("signUpStep2"," onError")
                        try{
                            if((e as HttpException).code() == 400){
                                var errorBody = (e as HttpException).response().errorBody()
                                var body = FuncUtil.cloneResponseBody(errorBody)
                                var bodyString = FuncUtil.getBodyAsString(body)
                                var errorJson = JSONObject(bodyString)
                                var errorCode = errorJson.getString("errors").toString()
                                signupView.onSignUpFailer(errorCode)
                            }else{
                                signupView.onSignUpFailer("TECHNICAL_ERROR")
                            }
                        }catch(e : Exception){
                            signupView.onSignUpFailer("TECHNICAL_ERROR")
                        }

                        signupView.hideLoader()
                        e.printStackTrace()
                    }
                });
    }

}
