package com.neoxia.myProject.service

import android.util.Log
import com.google.firebase.perf.FirebasePerformance
import com.neoxia.myProject.R
import com.neoxia.myProject.api.HttpApi
import com.neoxia.myProject.model.SignInRequest
import com.neoxia.myProject.model.SignInResponse
import com.neoxia.myProject.ui.signin_signup.SignInActivity
import com.neoxia.myProject.ui.signin_signup.SignInView
import com.neoxia.myProject.util.Constants
import com.neoxia.myProject.util.FuncUtil
import io.reactivex.Scheduler
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.observers.DisposableObserver
import io.reactivex.schedulers.Schedulers
import okhttp3.ResponseBody
import retrofit2.*

import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory

class SignInService(var signinView: SignInView) {
    var httpApi: HttpApi

    init {
        httpApi = FuncUtil.createBuider(signinView as SignInActivity)
    }

    fun login(signInRequest: SignInRequest) {
        signinView.showLoader()
        val myTrace = FirebasePerformance.getInstance().newHttpMetric(Constants.API_URL+"/api/login","Sign in")
        myTrace.start()
        httpApi.signIn(signInRequest).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<SignInResponse?>() {
                    override fun onComplete() {
                        myTrace.stop()
                    }

                    override fun onNext(t: SignInResponse) {
                        Log.d("SignInService"," onNext")
                        signinView.onLoginSuccess(t.adherent)
                        signinView.hideLoader()
                    }

                    override fun onError(e: Throwable) {
                        Log.d("SignInService"," onError")
                        if ( e is HttpException ){
                            signinView.onLoginFailer(R.string.id_errone)
                        }else
                            signinView.onLoginFailer(R.string.ERROR_UNKNOWN)
                        e.printStackTrace()
                        signinView.hideLoader()
                    }
                });
    }


    fun loginWithToken(nax: String) {
        signinView.showLoader()
        val myTrace = FirebasePerformance.getInstance().newHttpMetric(Constants.API_URL+"/api/login","Sign in")
        myTrace.start()
        httpApi.getProfile(nax).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<SignInResponse?>() {
                    override fun onComplete() {
                        myTrace.stop()
                    }

                    override fun onNext(t: SignInResponse) {
                        Log.d("loginWithToken"," onNext")
                        signinView.onLoginSuccess(t.adherent)
                        signinView.hideLoader()
                    }

                    override fun onError(e: Throwable) {
                        Log.d("loginWithToken"," onError")
                        e.printStackTrace()
                        signinView.hideLoader()
                    }
                });
    }

}
