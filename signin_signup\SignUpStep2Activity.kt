package com.neoxia.myProject.ui.signin_signup

import android.os.Bundle
import android.os.Handler
import android.view.View
import androidx.appcompat.app.AppCompatActivity

import com.neoxia.myProject.R
import com.neoxia.myProject.model.SignUpErrorCode
import com.neoxia.myProject.model.SignUpStep2Request
import com.neoxia.myProject.service.SignUpService
import com.neoxia.myProject.widget.ToastWithCustomStyle
import kotlinx.android.synthetic.main.activity_sign_up_step2.*

class SignUpStep2Activity : AppCompatActivity() , SignUpView{
    lateinit var signUpService: SignUpService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sign_up_step2)

        signUpService = SignUpService(this)
        val nax = intent.getStringExtra("nax")
        if(nax == null){
            ToastWithCustomStyle(this,getString(R.string.ERROR_UNKNOWN))
            finish()
        }
        validate.setOnClickListener {
            signupStep2(nax)
        }
    }


    fun signupStep2(nax : String){
        if (sms.text!!.isEmpty() || sms.text!!.length != 4) {
            smsError.visibility = View.VISIBLE
            smsError.requestFocus()
            return
        }
        var signupStep2Request = SignUpStep2Request(nax, sms.text.toString())
        signUpService.signUpStep2(signupStep2Request)
    }

    override fun onSignUpSuccess() {
        ToastWithCustomStyle(this, getString(R.string.account_create_success)).show()
        val handler = Handler()
        handler.postDelayed(Runnable {
            finish()
        }, 2000)
    }

    override fun onSignUpFailer(errorCode: String) {
        when (errorCode) {
            SignUpErrorCode.INCORRECT_SMS_CODE -> ToastWithCustomStyle(this, getString(R.string.incorrect_sms_code)).show()
            SignUpErrorCode.TECHNICAL_ERROR -> ToastWithCustomStyle(this, getString(R.string.ERROR_UNKNOWN)).show()
            else -> ToastWithCustomStyle(this, getString(R.string.ERROR_UNKNOWN)).show()
        }
    }

    override fun showLoader() {
        signupLoader.visibility = View.VISIBLE
    }

    override fun hideLoader() {
        signupLoader.visibility = View.GONE
    }

    override fun showMessage(index: Int) {
        ToastWithCustomStyle(this,getString(index))
    }

    override fun showMessage(msg: String) {
        ToastWithCustomStyle(this,msg)
    }

}