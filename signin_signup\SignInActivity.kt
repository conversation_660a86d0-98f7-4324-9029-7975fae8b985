package com.neoxia.myProject.ui.signin_signup

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.style.UnderlineSpan
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.CompoundButton
import android.widget.TextView
import com.google.firebase.analytics.FirebaseAnalytics
import com.neoxia.myProject.BaseActivity
import com.neoxia.myProject.R
import com.neoxia.myProject.model.Adherent
import com.neoxia.myProject.model.SignInRequest
import com.neoxia.myProject.service.SignInService
import com.neoxia.myProject.ui.home.HomeActivity
import com.neoxia.myProject.ui.reset_password.ResetPasswordActivity
import com.neoxia.myProject.util.FuncUtil
import com.neoxia.myProject.util.myProjectCache
import com.neoxia.myProject.widget.ToastWithCustomStyle
import com.scottyab.rootbeer.RootBeer
import kotlinx.android.synthetic.main.activity_sign_in.iconcheck
import kotlinx.android.synthetic.main.activity_sign_in.signinIdentifiantError
import kotlinx.android.synthetic.main.activity_sign_in.signinLoader
import kotlinx.android.synthetic.main.activity_sign_in.signinPassword
import kotlinx.android.synthetic.main.activity_sign_in.signinPasswordError
import kotlinx.android.synthetic.main.activity_sign_in.signinPasswordForgotten
import kotlinx.android.synthetic.main.activity_sign_in.signinRememberme
import kotlinx.android.synthetic.main.activity_sign_in.signinUsername
import okhttp3.ResponseBody

class SignInActivity : BaseActivity(), SignInView, View.OnClickListener {


    lateinit var signInService: SignInService
    lateinit var cache : myProjectCache

    companion object {
        var adherent: Adherent? = null
        var dossierPdf : ResponseBody? = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sign_in)
        checkRoot()
        signInService = SignInService(this)
        cache = myProjectCache(this)
        initListener()
        val content = SpannableString(resources.getString(R.string.password_forgotten))
        content.setSpan(UnderlineSpan(), 0, content.length, 0)
        signinPasswordForgotten.text = content

        findViewById<TextView>(R.id.signinSignup).setOnClickListener {
            val i = Intent(this, SignUpActivity::class.java)
            startActivity(i)
        }

        findViewById<Button>(R.id.signinValidate).setOnClickListener {
            /*cache = myProjectCache(this)
            cache.putString("nax","119235")
            var i = Intent(this, HomeActivity::class.java)
            startActivity(i)*/
            login()
        }

        signinUsername.setText(cache.getString("nax"))

        if(cache.getBoolean("rememberme"))
            signInService.loginWithToken(cache.getString("nax"))
        FuncUtil.hideKeyBoard(this)

    }
    private fun checkRoot() {
        val rootBeer = RootBeer(this)

        if (rootBeer.isRooted) {
            AlertDialog.Builder(this)
                .setTitle("Security Alert")
                .setMessage("This device appears to be rooted. For your safety, the app cannot run.")
                .setPositiveButton("Exit") { _, _ -> finishAffinity() }
                .setCancelable(false)
                .show()
        } else {
            Log.d("RootCheck", "No indication of root found")
        }
    }
    fun initListener() {
        signinPasswordForgotten.setOnClickListener(this@SignInActivity)
        signinRememberme.setOnCheckedChangeListener(object : CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
                //FuncUtil.sendNotification(this@SignInActivity, Environment.getExternalStorageDirectory().toString() + "/myProject_DOSSIERS" + "/test.pdf")
                iconcheck.visibility = if(signinRememberme.isChecked) View.VISIBLE else View.GONE
            }
        })
    }

    override fun onLoginSuccess(adherent: Adherent?) {
        //ToastWithCustomStyle(this, "Login Success").show()
        SignInActivity.adherent = adherent
        if(signinRememberme.isChecked)
            cache.putBoolean("rememberme",true)
        else
            cache.putBoolean("rememberme",false)
        cache.putString("nax",adherent?.NAX.toString())
        var i = Intent(this, SignInStep2Activity::class.java)
        startActivity(i)
        finish()
    }

    override fun showLoader() {
        signinLoader.visibility = View.VISIBLE
    }

    override fun hideLoader() {
        signinLoader.visibility = View.GONE
    }

    override fun showMessage(index: Int) {
        ToastWithCustomStyle(this, resources.getString(index)).show()
    }

    override fun showMessage(msg: String) {
        ToastWithCustomStyle(this, msg).show()
    }

    override fun onLoginFailer(msg: Int) {
        ToastWithCustomStyle(this, getString(msg)).show()
    }

    fun login() {
        FuncUtil.hideKeyBoard(this)
        hideErrorMessages()
        if (signinUsername!!.text!!.isEmpty()) {
            signinIdentifiantError.visibility = View.VISIBLE
            return
        }

        if (signinPassword!!.text!!.isEmpty()) {
            signinPasswordError.visibility = View.VISIBLE
            return
        }

        val signInRequest = SignInRequest(signinUsername!!.text.toString(), signinPassword!!.text.toString(),signinRememberme.isChecked)
        signInService.login(signInRequest);
    }

    fun hideErrorMessages() {
        signinIdentifiantError.visibility = View.GONE
        signinPasswordError.visibility = View.GONE
    }


    // Click listener

    override fun onClick(v: View?) {
        if(v!!.id == R.id.signinPasswordForgotten) {
            var intent = Intent(this, ResetPasswordActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
        }
    }

    override fun onResume() {
        super.onResume()

        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, "LOGIN")
        FirebaseAnalytics .getInstance (this).logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }
}
