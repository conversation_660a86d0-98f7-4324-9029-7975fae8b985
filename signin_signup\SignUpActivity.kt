package com.neoxia.myProject.ui.signin_signup

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.analytics.FirebaseAnalytics
import com.neoxia.myProject.R
import com.neoxia.myProject.model.SignUpErrorCode
import com.neoxia.myProject.model.SignUpStep1Request
import com.neoxia.myProject.service.SignUpService
import com.neoxia.myProject.util.Constants
import com.neoxia.myProject.util.FuncUtil
import com.neoxia.myProject.util.WebViewTrustAll
import com.neoxia.myProject.widget.ToastWithCustomStyle
import com.orhanobut.dialogplus.DialogPlus
import com.orhanobut.dialogplus.ViewHolder
import kotlinx.android.synthetic.main.activity_sign_up_step1.*
import kotlinx.android.synthetic.main.cgu_layout.view.*



class SignUpActivity : AppCompatActivity(), SignUpView {

    var acceptCGU = false
    var cguLayout: View? = null
    var idChanged = false
    var cguDialog: DialogPlus? = null

    lateinit var signUpService: SignUpService
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sign_up_step1)

        signUpService = SignUpService(this)

        findViewById<RelativeLayout>(R.id.signupRadio).setOnClickListener {
            if (acceptCGU) {
                uncheckedCGU()
            } else {
                // checkedCGU()

                // open CGU POP-UP
                showCguDialog()

            }
        }
        findViewById<Button>(R.id.signupValidate).setOnClickListener {
            hideErrors()
            signUp()
        }
        findViewById<LinearLayout>(R.id.signupBack).setOnClickListener {
            finish()
        }
        findViewById<EditText>(R.id.signupIdentifiant).addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                idChanged = true
            }

        })
        findViewById<EditText>(R.id.signupNAX).addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!idChanged) {
                    signupIdentifiant.text = signupNAX.text
                    idChanged = false
                }
            }

        })
        setInfo()
        //FuncUtil.hideKeyBoard(this)
    }

    fun signUp() {

        /*val i = Intent(this, SignUpStep2Activity::class.java)
        i.putExtra("nax",signupNAX.text)
        startActivity(i)
        finish()*/
        hideErrors()
        var hasError = false
        if (signupNAX.text!!.isEmpty()) {
            signupNAXError.visibility = View.VISIBLE
            signupNAXError.requestFocus()
            hasError = true
            //return
        }
        if (signupCIN.text!!.isEmpty()) {
            signupCINError.visibility = View.VISIBLE
            hasError = true
        }
        if (!FuncUtil.isPhoneValid(signupTel.text.toString())) {
            signupTelError.visibility = View.VISIBLE
            signupTelError.text = getString(R.string.tel_incorrect_form)
            hasError = true
        }
        if (signupIdentifiant.text!!.isEmpty()) {
            signupIdentifiantError.visibility = View.VISIBLE
            hasError = true
        }
        if (signupPassword.text!!.isEmpty()) {
            signupPasswordError.visibility = View.VISIBLE
            hasError = true
        }
        if (signupRepassword.text!!.isEmpty()) {
            signupRepasswordError.text = getString(R.string.veuillez_remplir_ce_champ)
            signupRepasswordError.visibility = View.VISIBLE
            hasError = true
        }
        if (!signupRepassword.text!!.toString().equals(signupPassword.text.toString())) {
            signupRepasswordError.text = getString(R.string.password_notequal)
            signupRepasswordError.visibility = View.VISIBLE
            hasError = true
        }
        if (!acceptCGU) {
            signupCGUError.visibility = View.VISIBLE
            hasError = true
        }

        if (hasError)
            return
        var signupStep1Request = SignUpStep1Request(signupNAX.text.toString(), signupCIN.text.toString(), signupTel.text.toString(), signupIdentifiant.text.toString(), signupPassword.text.toString())
        signUpService.signUpStep1(signupStep1Request)
    }

    fun checkedCGU() {
        acceptCGU = true
        signupCheckedIcon.visibility = View.VISIBLE
    }

    fun uncheckedCGU() {
        acceptCGU = false
        signupCheckedIcon.visibility = View.GONE
    }

    fun hideErrors() {
        signupNAXError.visibility = View.GONE
        signupCINError.visibility = View.GONE
        signupTelError.visibility = View.GONE
        signupIdentifiantError.visibility = View.GONE
        signupPasswordError.visibility = View.GONE
        signupRepasswordError.visibility = View.GONE
        signupCGUError.visibility = View.GONE
    }

    override fun onSignUpSuccess() {
        val i = Intent(this, SignUpStep2Activity::class.java)
        i.putExtra("nax",signupNAX.text.toString())
        startActivity(i)
        finish()
    }

    override fun onSignUpFailer(errorCode: String) {
        when (errorCode) {
            SignUpErrorCode.LOGIN_EXISTS -> ToastWithCustomStyle(this, getString(R.string.login_existe)).show()
            SignUpErrorCode.ACCOUNT_EXISTS -> ToastWithCustomStyle(this, getString(R.string.account_exist)).show()
            SignUpErrorCode.INCORRECT_NAX -> ToastWithCustomStyle(this, getString(R.string.CU001_M002)).show()
            SignUpErrorCode.INCORRECT_CIN -> ToastWithCustomStyle(this, getString(R.string.CU001_M006)).show()
            SignUpErrorCode.EMPTY_PHONE -> ToastWithCustomStyle(this, getString(R.string.CU001_M005)).show()
            SignUpErrorCode.INCORRECT_PHONE -> ToastWithCustomStyle(this, getString(R.string.CU001_M005)).show()
            SignUpErrorCode.TECHNICAL_ERROR -> ToastWithCustomStyle(this, getString(R.string.ERROR_UNKNOWN)).show()
            else -> ToastWithCustomStyle(this, getString(R.string.ERROR_UNKNOWN)).show()
        }
    }

    override fun showLoader() {
        signupLoader.visibility = View.VISIBLE
    }

    override fun hideLoader() {
        signupLoader.visibility = View.GONE
    }

    override fun showMessage(index: Int) {
    }

    override fun showMessage(msg: String) {
    }

    fun setInfo() {
        /*signupNAX.setText("600175")
        signupIdentifiant.setText("manouare")
        signupPassword.setText("test123")
        signupRepassword.setText("test123")
        signupCIN.setText("EEE531817")
        signupTel.setText("072778755")
        idChanged = false*/
    }

    fun showCguDialog() {

        if (cguDialog == null) {
            cguLayout = LayoutInflater.from(this@SignUpActivity).inflate(R.layout.cgu_layout, null)

            if (cguLayout != null)
                cguDialog = DialogPlus.newDialog(this)
                        .setContentHolder(object : ViewHolder(cguLayout) {})
                        .setCancelable(false)
                        .setGravity(Gravity.CENTER)
                        .setMargin(0, 0, 0, 0)
                        .create()
        }

        cguLayout!!.acceptCGUBtn.setOnClickListener {
            cguDialog!!.dismiss()
            checkedCGU()
        }

        cguLayout!!.cguContent.webViewClient = WebViewTrustAll(this)
        cguLayout!!.cguContent.loadUrl(Constants.CGU_URL)

        cguDialog!!.show()
    }

    override fun onResume() {
        super.onResume()

        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, "CREATION_COMPTE")
        FirebaseAnalytics .getInstance (this).logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }
}

