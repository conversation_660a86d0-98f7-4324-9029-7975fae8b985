package com.neoxia.myProject.api

import com.google.firebase.perf.metrics.AddTrace
import com.neoxia.myProject.model.*
import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.ResponseBody

import retrofit2.http.*

interface HttpApi {

    @POST("/api/login")
    fun signIn(@Body request: SignInRequest): Observable<SignInResponse?>

    @POST("/api/loginStep2")
    fun signInStep2(@Body request: SignIn2Request): Observable<SignInResponse?>

    @GET("/api/profile")
    fun getProfile(@Query("nax") nax: String): Observable<SignInResponse?>

    @POST("/api/signupStep1")
    fun signUpStep1(@Body request: SignUpStep1Request): Observable<SignUpResponse?>

    @POST("/api/signupStep2")
    fun signUpStep2(@Body request: SignUpStep2Request): Observable<SignUpResponse?>

    @GET("/api/news")
    fun getNews(): Observable<List<NewsItemData>?>

    @GET("/api/links")
    fun getLinks(): Observable<List<URLItemData>?>

    @GET("/api/registerDeviceToken")
    fun registerDeviceToken(@Query("deviceToken") deviceToken:String): Observable<ResponseBody>

    @GET("/api/situation")
    fun getFSituation(@Query("nax") nax:String): Observable<Beneficiaires>

    @GET("/api/SADocuments")
    fun getRefundData(@Query("nax") nax:String): Observable<RefundData>

    @GET("/api/PCDocuments")
    fun getASupportData(@Query("nax") nax:String): Observable<RefundData>

    @GET("/api/EPDocuments")
    fun getPCommitmentData(@Query("nax") nax:String): Observable<RefundData>

    @GET("/api/CAADDocuments")
    fun getCaadData(@Query("nax") nax:String): Observable<CaadData>

    @POST("api/uploadProfilePicture")
    @Multipart
    fun changeProfile(@Query("nax") nax:String, @Part file : MultipartBody.Part?): Observable<ChangeProfileResponse>

    @POST("api/uploadProfilePicture")
    fun setAvatar(@Query("nax") nax:String, @Query("avatar")  avatar:String?): Observable<ChangeProfileResponse>

    @GET("storage/downloads/{fileName}")
    fun download(@Path("fileName") fileName:String): Observable<ResponseBody>

    @GET("api/exportPDF")
    fun downloadDossierSA(@Query("id") id:String,@Query("nax") nax:String): Observable<ResponseBody>

    @GET("api/center-information")
    fun getInformationCenterData(): Observable<InformationCenterData>

    @GET("api/assistance")
    fun getAssistanatData(): Observable<AssistanatData>

    @POST("api/seenNotification")
    fun seenNotification(@Query("id") id:Int): Observable<Any>

    @POST("api/reset-password/step1")
    fun resetPasswordStep1(@Body request: RestPasswordRequest): Observable<RestPasswordResponse?>

    @POST("api/reset-password/step2")
    fun resetPasswordStep2(@Body request: RestPasswordRequest): Observable<RestPasswordResponse?>

    @POST("api/reset-password/step3")
    fun resetPasswordStep3(@Body request: RestPasswordRequest): Observable<RestPasswordResponse?>

    @GET("api/version")
    fun version(): Observable<SettingResponse?>
}
